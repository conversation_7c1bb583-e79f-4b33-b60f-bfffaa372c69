<?php

namespace App\Models;

use Illuminate\Auth\Authenticatable;
use Illuminate\Auth\MustVerifyEmail;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Foundation\Auth\Access\Authorizable;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends BaseModel implements
    AuthenticatableContract,
    AuthorizableContract,
    CanResetPasswordContract
{
    use Authenticatable, Authorizable, CanResetPassword, MustVerifyEmail;
    use HasApiTokens;
    use HasFactory;
    use Notifiable;

    const DISABLE = 0;
    const ACTIVE = 1;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'avatar',
        'password',
        'status',
        'last_login_at',
        'age',
        'preferred_subjects',
        'preferred_grades',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'password' => 'hashed',
            'preferred_subjects' => 'array',
            'preferred_grades' => 'array',
        ];
    }

    public function sanctumTokenApi($expires_at)
    {
        $this->tokens()->delete();

        return $this->createToken(
            'api_token',
            ['*'],
            $expires_at
        )->plainTextToken;
    }

    public function cookieUserApi()
    {
        $now = now();
        $expiresAt = $now->copy()->addWeek(); // Token sống 7 ngày
        $userInfo = base64_encode(json_encode([
            'user' => [
                'id' => $this->id,
                'name' => $this->name,
                'email' => $this->email,
                'role' => $this->roles->pluck('name'),
            ],
            'token' => $this->sanctumTokenApi($expiresAt),
        ]));
        // 'name', 'value', $minutes, $path, $domain, $secure, $httpOnly
        return cookie(
            config('app.user_cookie_name'),
            $userInfo,
            $now->diffInMinutes($expiresAt, true), // Thời gian sống của cookie = Thời gian sống của token
        );
    }

    /**
     * Return the user's roles
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class)->withTimestamps();
    }

    /**
     * Check if the user has a role
     */
    public function hasRole(string $role): bool
    {
        return $this->roles->where('name', $role)->isNotEmpty();
    }

    public function quizResults()
    {
        return $this->hasMany(QuizResult::class);
    }

    public function classrooms()
    {
        return $this->belongsToMany(Classroom::class, 'classroom_user');
    }

    public function classroomUser()
    {
        return $this->hasMany(ClassroomUser::class, 'user_id');
    }

    /**
     * Check if the user has role admin
     */
    public function isAdmin(): bool
    {
        return $this->hasRole(Role::ROLE_ADMIN);
    }

    /**
     * Check if the user has role editor
     */
    public function isEditor(): bool
    {
        return $this->hasRole(Role::ROLE_EDITOR);
    }

    /**
     * Check if the user has role teacher
     */
    public function isTeacher(): bool
    {
        return $this->hasRole(Role::ROLE_TEACHER);
    }

    /**
     * Check if the user has role student
     */
    public function isStudent(): bool
    {
        return $this->hasRole(Role::ROLE_STUDENT);
    }

    /**
     * Check if the user can be an administrators
     */
    public function canBeAdministrators(): bool
    {
        return $this->isAdmin() || $this->isEditor();
    }

    /**
     * Get the collections that belong to the user.
     */
    public function collections()
    {
        return $this->hasMany(Collection::class);
    }

    /**
     * Get the user's preferred subjects (multi-selection)
     */
    public function preferredSubjectsRelation()
    {
        if (!$this->preferred_subjects) {
            return collect();
        }

        return Subject::whereIn('id', $this->preferred_subjects)->get();
    }

    /**
     * Get the user's preferred grades (multi-selection)
     */
    public function preferredGradesRelation()
    {
        if (!$this->preferred_grades) {
            return collect();
        }

        return Grade::whereIn('id', $this->preferred_grades)->get();
    }
}
