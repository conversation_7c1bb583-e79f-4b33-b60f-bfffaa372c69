<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\Role;
use App\Models\Collection;
use App\Models\TableContent;
use App\Http\Requests\Api\UserProfileRequest;
use App\Http\Requests\Api\PasswordRequest;
use App\Http\Resources\UserResource;
use App\Http\Resources\CollectionResource;
use Illuminate\Http\JsonResponse;
use Laravel\Sanctum\PersonalAccessToken;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class UserController extends BaseController
{
    /**
     * @param Request $request
     * @return UserResource
     */
    public function currentUser(Request $request)
    {
        $user = $request->user()->load('roles');
        $shouldRefresh = $request->boolean('refreshToken');

        if (!$shouldRefresh) {
            $infoCookie = $request->cookie(config('app.user_cookie_name'));

            if ($infoCookie) {
                $userInfo = json_decode(base64_decode($infoCookie), true);
                $token = $userInfo['token'] ?? null;

                if ($token && preg_match('/^(\d+)\|/', $token, $matches)) {
                    $accessToken = PersonalAccessToken::where('tokenable_type', User::class)
                        ->where('tokenable_id', $user->id)
                        ->where('id', $matches[1])
                        ->first();
                    $shouldRefresh = !$accessToken || !$accessToken->expires_at || now()->greaterThan($accessToken->expires_at);
                } else {
                    $shouldRefresh = true;
                }
            } else {
                $shouldRefresh = true;
            }
        }

        if ($shouldRefresh) {
            $cookie = $user->cookieUserApi();

            return (new UserResource($user))->response($request)->withCookie($cookie);
        }

        return new UserResource($user);
    }

    /**
     * @param UserProfileRequest $request
     * @return UserResource
     */
    public function updateProfile(UserProfileRequest $request)
    {
        $user = auth()->user();

        $inputs = $request->all([
            'name',
            'email',
            'phone',
            'avatar',
        ]);

        $user->update($inputs);

        return new UserResource($user);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updatePassword(PasswordRequest $request)
    {
        auth()->user()->update(['password' => Hash::make($request->password)]);

        return $this->jsonResponse('Đổi mật khẩu thành công');
    }

    public function myLibrary()
    {
        $userId = auth()->id();
        $totalQuizzes = TableContent::where('editor_id', $userId)
            ->where('type', TableContent::TYPE_EXAM_QUIZ)
            ->count();
        $collections =  Collection::where('user_id', $userId)
            ->withCount('tableContents')
            ->get();

        return $this->jsonResponse('OK', [
            'data' => [
                'totalQuizzes' => $totalQuizzes,
                'likedQuizzes' => 0, // chưa làm
                'collections' => CollectionResource::collection($collections),
            ]
        ]);
    }

    /**
     * Assign role to user with additional profile data
     *
     * @param Request $request
     * @return UserResource|JsonResponse
     */
    public function assignRole(Request $request): UserResource | \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'role' => 'required|string|in:student,teacher,admin,editor',
            'age' => 'nullable|integer|min:5|max:100',
            // Support both single and multi-selection for backward compatibility
            'subject_ids' => 'nullable|array|min:1',
            'subject_ids.*' => 'integer|exists:subjects,id',
            'grade_ids' => 'nullable|array|min:1',
            'grade_ids.*' => 'integer|exists:grades,id',
        ]);

        $user = auth()->user();

        // Check if user already has a role
        if ($user->roles()->exists()) {
            return response()->json([
                'message' => 'Người dùng đã có vai trò được gán'
            ], 400);
        }

        DB::transaction(function () use ($request, $user) {
            // Find and assign the role
            $role = Role::where('name', $request->role)->first();
            if ($role) {
                $user->roles()->attach($role->id);
            }

            // Update user profile with additional data
            $profileData = [];

            if ($request->has('age')) {
                $profileData['age'] = $request->age;
            }

            if ($request->has('subject_ids')) {
                $profileData['preferred_subjects'] = $request->subject_ids;
            }

            if ($request->has('grade_ids')) {
                $profileData['preferred_grades'] = $request->grade_ids;
            }

            if (!empty($profileData)) {
                $user->update($profileData);
            }
        });

        // Reload user with roles
        $user->load('roles');

        return new UserResource($user);
    }
}
