<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->integer('age')->nullable()->after('avatar');
            $table->unsignedBigInteger('preferred_subject_id')->nullable()->after('age');
            $table->unsignedBigInteger('preferred_grade_id')->nullable()->after('preferred_subject_id');
            
            $table->foreign('preferred_subject_id')->references('id')->on('subjects')->onDelete('set null');
            $table->foreign('preferred_grade_id')->references('id')->on('grades')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['preferred_subject_id']);
            $table->dropForeign(['preferred_grade_id']);
            $table->dropColumn(['age', 'preferred_subject_id', 'preferred_grade_id']);
        });
    }
};
