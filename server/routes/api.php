<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\HomeController;
use App\Http\Controllers\Api\GradeController;
use App\Http\Controllers\Api\SubjectController;
use App\Http\Controllers\Api\CourseController;
use App\Http\Controllers\Api\TocController;
use App\Http\Controllers\Api\TableContentController;
use App\Http\Controllers\Api\SuggestController;
use App\Http\Controllers\Api\ClassroomController;
use App\Http\Controllers\Api\QuestionController;
use App\Http\Controllers\Api\QuizResultController;
use App\Http\Controllers\Api\CollectionController;
// Private route

// Check user bằng xác thực cookie based session authentication để tạo mã token cho xác thực api token
// riêng api này ko cho xác thực bằng api token để tăng bảo mật
Route::get('/user', [UserController::class, 'currentUser'])->middleware('auth:web');
//  Xác thực api token
Route::middleware(['auth:sanctum'])->group(function () {
    Route::get('/user/recent-activity', [HomeController::class, 'recentActivity']);
    Route::put('/user/update-profile', [UserController::class, 'updateProfile']);
    Route::post('/user/change-password', [UserController::class, 'updatePassword']);
    Route::get('/user/my-library', [UserController::class, 'myLibrary']);

    Route::group(['prefix' => 'private'], function () {
        Route::apiResource('quiz', TableContentController::class);
        Route::post('quiz/{quiz}/assign-quiz', [TableContentController::class, 'assignQuiz']);
        Route::post('quiz/{quiz}/duplicate-questions', [TableContentController::class, 'duplicateQuestions']);
        Route::get('quiz/{quiz}/classrooms', [TableContentController::class, 'getClassrooms']);
        Route::post('quiz/{quiz}/assign-classrooms', [TableContentController::class, 'assignClassrooms']);
        Route::post('quiz/{quiz}/copy', [TableContentController::class, 'copyQuiz']);
        Route::put('quiz/{quiz}/update-title', [TableContentController::class, 'updateTitle']);
        Route::patch('quiz/{quiz}/remove-from-folder', [TableContentController::class, 'removeFromFolder']);
        Route::apiResource('quiz.question', QuestionController::class);

        Route::apiResource('classrooms', ClassRoomController::class);

        Route::get('classrooms/{classroom}/students', [ClassroomController::class, 'getStudents']);
        Route::post('classrooms/{classroom}/students', [ClassroomController::class, 'storeStudent']);
        Route::put('classrooms/{classroom}/students/{studentId}/update', [ClassroomController::class, 'updateStudent']);
        Route::delete('classrooms/{classroom}/students/{studentId}', [ClassroomController::class, 'destroyStudent']);
        Route::post('classrooms/{classroom}/remove-students', [ClassroomController::class, 'removeStudents']);
        Route::get('classrooms/{classroom}/assignments', [ClassroomController::class, 'getAssignments']);
        Route::put('classrooms/{classroom}/assignments/{assignment}/update', [ClassroomController::class, 'updateAssignment']);
        Route::delete('classrooms/{classroom}/assignments/{assignment}', [ClassroomController::class, 'destroyAssignment']);
        Route::delete('classrooms/assignments/delete/report', [ClassroomController::class, 'destroyAssignmentsReport']);

        Route::post('classrooms/join', [ClassroomController::class, 'joinClass']);
        Route::post('classrooms/{classroom}/leave', [ClassroomController::class, 'leaveClassroom']);

        Route::post('classrooms/{classroom}/students/import', [ClassroomController::class, 'importStudents']);
        Route::get('classrooms/{classroom}/students/export', [ClassroomController::class, 'exportStudents']);

        Route::get('assignments/report', [TableContentController::class, 'getAssignmentsReport']);

        Route::get('classrooms/{classroom}/students/{student}/report', [TableContentController::class, 'getStudentReport']);
        Route::get('assignments/{assignment}/report', [TableContentController::class, 'getAssignmentReport']);

        Route::get('my-classrooms', [ClassroomController::class, 'myClassrooms']);
        Route::get('my-classroom/{classroom}', [ClassroomController::class, 'myClassroomDetail']);

        Route::apiResource('collections', CollectionController::class);
        Route::post('collections/{collection}/add-quiz/{quiz}', [CollectionController::class, 'addQuiz']);
        Route::delete('collections/{collection}/remove-quiz/{quiz}', [CollectionController::class, 'removeQuiz']);

        Route::apiResource('courses', CourseController::class);
        Route::put('courses/{course}/update-positions', [CourseController::class, 'updatePositions']);
        Route::get('/courses/{course}/tocs', [CourseController::class, 'getTocsOfCourse']);

        Route::post('/tocs', [TocController::class, 'create']);
        Route::put('/tocs/{toc}/update-title', [TocController::class, 'updateTitle']);
        Route::delete('/tocs/{toc}', [TocController::class, 'destroy']);
    });
});

// Public route
Route::get('quiz/{quiz}/download', [TableContentController::class, 'downloadTableContent']);
Route::get('/quizzes', [TableContentController::class, 'quizzes']);
Route::get('/quizzes/{slug}', [TableContentController::class, 'getQuizBySlug']);

Route::get('/quiz/search-by-code', [TableContentController::class, 'getTableContentByCode']);
Route::get('/quiz/{quiz}', [TableContentController::class, 'quizInfo']);

Route::get('/grades', [GradeController::class, 'grades']);
Route::get('/grades/{grade:slug}/show', [GradeController::class, 'getGradeBySlug']);

Route::get('/subjects', [SubjectController::class, 'subjects']);
Route::get('/subjects/{subject:slug}/show', [SubjectController::class, 'getSubjectBySlug']);
Route::get('/subjects/{subject}/quizzes', [SubjectController::class, 'getQuizsOfSubject']);

Route::get('/courses', [CourseController::class, 'courses']);
Route::get('/courses/{course}', [CourseController::class, 'getCourse']);
Route::get('/courses/{course}/quizzes', [CourseController::class, 'getQuizsOfCourse']);

Route::get('/tocs/{toc}', [TocController::class, 'getToc']);
Route::get('/tocs/{toc}/quizzes', [TocController::class, 'getQuizsOfToc']);

Route::get('/worksheets/sidebar', [HomeController::class, 'sidebarWorksheets']);
Route::get('/worksheets/search', [HomeController::class, 'searchWorksheets']);

Route::get('/suggest', [SuggestController::class, 'suggest']);

// Public route can check auth
Route::middleware(['sanctum.api'])->group(function () {
    Route::group(['prefix' => 'play-quiz'], function () {
        Route::post('soloJoin', [QuizResultController::class, 'soloJoin']);
        // comment: nếu sửa url của các route này cần sửa cả trong App\Http\Resources\QuestionResource
        Route::get('{quizResult}/info', [QuizResultController::class, 'quizResultInfo']);
        // end comment
        Route::post('{quizResult}/soloProceed', [QuizResultController::class, 'soloProceed']);
        Route::post('{quizResult}/soloEnd', [QuizResultController::class, 'soloEnd']);
    });
});
