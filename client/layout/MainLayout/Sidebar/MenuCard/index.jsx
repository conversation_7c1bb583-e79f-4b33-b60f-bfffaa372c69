import { memo } from 'react';
import { useSelector } from "react-redux";

// material-ui
import { useTheme } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import Card from '@mui/material/Card';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

import Image from "next/image";

// ==============================|| SIDEBAR - MENU CARD ||============================== //

function MenuCard() {
  const theme = useTheme();
  const { user } = useSelector((state) => state.auth);

  return user ? (
    <Card
      sx={{
        bgcolor: 'primary.light',
        mb: 2.75,
        overflow: 'hidden',
        position: 'relative',
        '&:after': {
          content: '""',
          position: 'absolute',
          width: 157,
          height: 157,
          bgcolor: 'primary.200',
          borderRadius: '50%',
          top: -105,
          right: -96
        }
      }}
    >
      <Box sx={{ p: 2 }}>
        <List disablePadding sx={{ pb: 1 }}>
          <ListItem alignItems="center" sx={{ gap: 1 }} disableGutters disablePadding>
            <ListItemAvatar sx={{ mt: 0 }}>
              <Avatar sx={{ width: 56, height: 56 }}>
                {user.avatar ? (
                  <Image
                    src={user.avatar}
                    alt={user.name}
                    width={50}
                    height={50}
                  />
                ) : (
                  user.name?.charAt(0)
                )}
              </Avatar>
            </ListItemAvatar>
            <ListItemText
              primary={
                <Typography variant="subtitle1" sx={{ color: 'primary.800' }}>
                  {user.name}
                </Typography>
              }
              secondary={
                <Typography variant="caption">
                  Tài khoản { user.role?.display_name || user.role?.name }
                </Typography>
              }
            />
          </ListItem>
        </List>
      </Box>
    </Card>
  ) : null;
}

export default memo(MenuCard);
