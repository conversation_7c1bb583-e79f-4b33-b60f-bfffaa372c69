"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/auth';
import WelcomeDialogSystem from '@/components/welcome/WelcomeDialogSystem';

const DashboardClient = ({
  children,
  availableRoles = ['student', 'teacher']
}) => {
  const { user, userLoading } = useAuth();
  const [showWelcomeDialog, setShowWelcomeDialog] = useState(false);

  useEffect(() => {
    // Check if user needs to see welcome dialog
    if (!userLoading && user) {
      // Check if user has no roles assigned
      const hasRoles = user.roles && user.roles.length > 0;

      if (!hasRoles) {
        setShowWelcomeDialog(true);
      }
    }
  }, [user, userLoading]);

  const handleWelcomeComplete = () => {
    setShowWelcomeDialog(false);
    // Optionally refresh the page or update user data
    window.location.reload();
  };

  return (
    <>
      {children}
      {showWelcomeDialog && (
        <WelcomeDialogSystem
          user={user}
          availableRoles={availableRoles}
          onComplete={handleWelcomeComplete}
        />
      )}
    </>
  );
};

export default DashboardClient;
