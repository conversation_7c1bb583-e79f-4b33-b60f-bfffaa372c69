"use client";

import React, { useState } from 'react';
import {
  DialogActions,
  Box,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  CircularProgress,
  Alert,
  Grid
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import BaseRoleWelcomeDialog from './BaseRoleWelcomeDialog';

const TrainerWelcomeDialog = ({ open, onComplete, onBack, loading }) => {
  const theme = useTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const [specialization, setSpecialization] = useState('');
  const [experience, setExperience] = useState('');
  const [certification, setCertification] = useState('');

  const steps = ['Thông tin chuyên môn', '<PERSON><PERSON><PERSON> thành'];

  const specializations = [
    { value: 'academic_training', label: 'Đ<PERSON>o tạo học thuật' },
    { value: 'skill_training', label: 'Đào tạo kỹ năng' },
    { value: 'professional_training', label: 'Đào tạo nghề nghiệp' },
    { value: 'corporate_training', label: 'Đào tạo doanh nghiệp' }
  ];

  const experienceLevels = [
    { value: '0-2', label: '0-2 năm' },
    { value: '3-5', label: '3-5 năm' },
    { value: '6-10', label: '6-10 năm' },
    { value: '10+', label: 'Trên 10 năm' }
  ];

  const handleNext = () => {
    if (currentStep === 0 && specialization && experience) {
      setCurrentStep(1);
    }
  };

  const handleBack = () => {
    if (currentStep === 0) {
      onBack();
    } else {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    if (specialization && experience && !loading) {
      onComplete({
        role: 'trainer',
        specialization,
        experience,
        certification
      });
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <>
            <Typography variant="body1" color="text.secondary" mb={4} textAlign="center">
              Hãy cho chúng tôi biết về chuyên môn và kinh nghiệm đào tạo của bạn
            </Typography>

            <Grid container spacing={3} mb={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="specialization-select-label">Chuyên môn đào tạo *</InputLabel>
                  <Select
                    labelId="specialization-select-label"
                    value={specialization}
                    label="Chuyên môn đào tạo *"
                    onChange={(e) => setSpecialization(e.target.value)}
                    disabled={loading}
                  >
                    {specializations.map((spec) => (
                      <MenuItem key={spec.value} value={spec.value}>
                        {spec.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="experience-select-label">Kinh nghiệm *</InputLabel>
                  <Select
                    labelId="experience-select-label"
                    value={experience}
                    label="Kinh nghiệm *"
                    onChange={(e) => setExperience(e.target.value)}
                    disabled={loading}
                  >
                    {experienceLevels.map((exp) => (
                      <MenuItem key={exp.value} value={exp.value}>
                        {exp.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Chứng chỉ/Bằng cấp (tùy chọn)"
                  value={certification}
                  onChange={(e) => setCertification(e.target.value)}
                  disabled={loading}
                  multiline
                  rows={3}
                  placeholder="Ví dụ: Thạc sĩ Giáo dục, Chứng chỉ đào tạo quốc tế..."
                />
              </Grid>
            </Grid>
          </>
        );

      case 1:
        return (
          <Box textAlign="center" py={4}>
            <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
              Chào mừng Trainer!
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={4}>
              Tài khoản trainer của bạn đã được thiết lập thành công. Hãy bắt đầu tạo các khóa đào tạo!
            </Typography>
            <Alert severity="info" sx={{ mb: 3 }}>
              Bạn có thể tạo và quản lý các khóa đào tạo chuyên nghiệp
            </Alert>
            {loading && (
              <Box display="flex" justifyContent="center" mb={2}>
                <CircularProgress size={24} />
              </Box>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  const renderActions = () => {
    return (
      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button
          onClick={handleBack}
          disabled={loading}
          startIcon={<ArrowBackIcon />}
          color="inherit"
        >
          Quay lại
        </Button>
        
        {currentStep === 0 ? (
          <Button
            onClick={handleNext}
            disabled={!specialization || !experience || loading}
            variant="contained"
            color="primary"
            size="large"
          >
            Tiếp tục
          </Button>
        ) : (
          <Button
            onClick={handleComplete}
            disabled={loading}
            variant="contained"
            color="primary"
            size="large"
            startIcon={loading ? <CircularProgress size={16} /> : <CheckCircleIcon />}
          >
            {loading ? 'Đang xử lý...' : 'Hoàn thành'}
          </Button>
        )}
      </DialogActions>
    );
  };

  return (
    <BaseRoleWelcomeDialog
      open={open}
      title="Thiết lập tài khoản Trainer"
      icon={<FitnessCenterIcon sx={{ fontSize: 48, color: theme.palette.warning.main }} />}
      steps={steps}
      currentStep={currentStep}
      onBack={onBack}
      loading={loading}
      actions={renderActions()}
    >
      {renderStepContent()}
    </BaseRoleWelcomeDialog>
  );
};

export default TrainerWelcomeDialog;
