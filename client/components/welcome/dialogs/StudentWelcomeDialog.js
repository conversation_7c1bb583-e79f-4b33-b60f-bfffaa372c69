"use client";

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SchoolIcon from '@mui/icons-material/School';

const StudentWelcomeDialog = ({ open, onComplete, onBack, loading }) => {
  const theme = useTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedAge, setSelectedAge] = useState('');

  const steps = ['Chọn độ tuổi', 'Hoàn thành'];

  // Age ranges for different educational levels
  const ageRanges = [
    { value: '6-10', label: '6-10 tuổi (Ti<PERSON><PERSON> học)', ages: [6, 7, 8, 9, 10] },
    { value: '11-14', label: '11-14 tuổi (THCS)', ages: [11, 12, 13, 14] },
    { value: '15-18', label: '15-18 tuổi (THPT)', ages: [15, 16, 17, 18] },
    { value: '18+', label: '18+ tuổi (Người lớn)', ages: [19, 20, 21, 22, 23, 24, 25] }
  ];

  const handleAgeChange = (event) => {
    setSelectedAge(event.target.value);
  };

  const handleNext = () => {
    if (currentStep === 0 && selectedAge) {
      setCurrentStep(1);
    }
  };

  const handleBack = () => {
    if (currentStep === 0) {
      onBack();
    } else {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    if (selectedAge && !loading) {
      // Convert age range to a specific age (use middle value)
      let ageValue;
      const range = ageRanges.find(r => r.value === selectedAge);
      if (range) {
        ageValue = Math.floor((range.ages[0] + range.ages[range.ages.length - 1]) / 2);
      }
      
      onComplete({
        role: 'student',
        age: ageValue
      });
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <>
            <Box textAlign="center" mb={4}>
              <Box mb={2}>
                <SchoolIcon sx={{ fontSize: 48, color: theme.palette.primary.main }} />
              </Box>
              <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
                Chọn độ tuổi của bạn
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Điều này giúp chúng tôi cung cấp nội dung phù hợp với trình độ của bạn
              </Typography>
            </Box>

            <Box mb={4}>
              <FormControl fullWidth>
                <InputLabel id="age-select-label">Độ tuổi</InputLabel>
                <Select
                  labelId="age-select-label"
                  value={selectedAge}
                  label="Độ tuổi"
                  onChange={handleAgeChange}
                  disabled={loading}
                >
                  {ageRanges.map((range) => (
                    <MenuItem key={range.value} value={range.value}>
                      {range.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </>
        );

      case 1:
        return (
          <Box textAlign="center" py={4}>
            <Box mb={2}>
              <SchoolIcon sx={{ fontSize: 48, color: theme.palette.primary.main }} />
            </Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
              Chào mừng bạn đến với 2048.vn!
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={4}>
              Tài khoản học sinh của bạn đã được thiết lập thành công. Hãy bắt đầu học tập ngay!
            </Typography>
            {loading && (
              <Box display="flex" justifyContent="center" mb={2}>
                <CircularProgress size={24} />
              </Box>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  const renderActions = () => {
    return (
      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button
          onClick={handleBack}
          disabled={loading}
          startIcon={<ArrowBackIcon />}
          color="inherit"
        >
          Quay lại
        </Button>
        
        {currentStep === 0 ? (
          <Button
            onClick={handleNext}
            disabled={!selectedAge || loading}
            variant="contained"
            color="primary"
            size="large"
          >
            Tiếp tục
          </Button>
        ) : (
          <Button
            onClick={handleComplete}
            disabled={loading}
            variant="contained"
            color="primary"
            size="large"
          >
            {loading ? 'Đang xử lý...' : 'Hoàn thành'}
          </Button>
        )}
      </DialogActions>
    );
  };

  return (
    <Dialog
      open={open}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 2
        }
      }}
    >
      <DialogContent sx={{ p: 4 }}>
        <Box mb={3}>
          <Stepper activeStep={currentStep} alternativeLabel>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        {renderStepContent()}
      </DialogContent>

      {renderActions()}
    </Dialog>
  );
};

export default StudentWelcomeDialog;
