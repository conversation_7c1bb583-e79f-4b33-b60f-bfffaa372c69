"use client";

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  Button,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

/**
 * Base component for role-specific welcome dialogs
 * Provides common structure and functionality that can be extended
 */
const BaseRoleWelcomeDialog = ({
  open,
  title,
  icon,
  steps = [],
  currentStep = 0,
  onBack,
  loading = false,
  children,
  actions,
  maxWidth = "sm",
  ...props
}) => {
  const theme = useTheme();

  const defaultActions = (
    <DialogActions sx={{ p: 3, pt: 0 }}>
      <Button
        onClick={onBack}
        disabled={loading}
        startIcon={<ArrowBackIcon />}
        color="inherit"
      >
        Quay lại
      </Button>
    </DialogActions>
  );

  return (
    <Dialog
      open={open}
      maxWidth={maxWidth}
      fullWidth
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 2
        }
      }}
      {...props}
    >
      <DialogContent sx={{ p: 4 }}>
        {/* Header with title and icon */}
        {(title || icon) && (
          <Box textAlign="center" mb={4}>
            {icon && (
              <Box mb={2}>
                {icon}
              </Box>
            )}
            {title && (
              <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
                {title}
              </Typography>
            )}
          </Box>
        )}

        {/* Step indicator */}
        {steps.length > 0 && (
          <Box mb={3}>
            <Stepper activeStep={currentStep} alternativeLabel>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>
        )}

        {/* Main content */}
        {children}
      </DialogContent>

      {/* Actions */}
      {actions || defaultActions}
    </Dialog>
  );
};

export default BaseRoleWelcomeDialog;
