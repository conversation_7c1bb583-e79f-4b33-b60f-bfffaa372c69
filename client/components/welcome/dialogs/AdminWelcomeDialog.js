"use client";

import React, { useState } from 'react';
import {
  DialogActions,
  Box,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import BaseRoleWelcomeDialog from './BaseRoleWelcomeDialog';

const AdminWelcomeDialog = ({ open, onComplete, onBack, loading }) => {
  const theme = useTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const [adminType, setAdminType] = useState('');

  const steps = ['Chọn loại quản trị', '<PERSON><PERSON><PERSON> thành'];

  const adminTypes = [
    { value: 'super_admin', label: 'Quản trị viên cấp cao' },
    { value: 'content_admin', label: 'Quản trị nội dung' },
    { value: 'user_admin', label: 'Quản trị người dùng' },
    { value: 'system_admin', label: 'Quản trị hệ thống' }
  ];

  const handleNext = () => {
    if (currentStep === 0 && adminType) {
      setCurrentStep(1);
    }
  };

  const handleBack = () => {
    if (currentStep === 0) {
      onBack();
    } else {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    if (adminType && !loading) {
      onComplete({
        role: 'admin',
        admin_type: adminType
      });
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <>
            <Typography variant="body1" color="text.secondary" mb={4} textAlign="center">
              Chọn loại quản trị viên phù hợp với vai trò của bạn
            </Typography>

            <Box mb={4}>
              <FormControl fullWidth>
                <InputLabel id="admin-type-select-label">Loại quản trị viên</InputLabel>
                <Select
                  labelId="admin-type-select-label"
                  value={adminType}
                  label="Loại quản trị viên"
                  onChange={(e) => setAdminType(e.target.value)}
                  disabled={loading}
                >
                  {adminTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </>
        );

      case 1:
        return (
          <Box textAlign="center" py={4}>
            <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
              Chào mừng Quản trị viên!
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={4}>
              Tài khoản quản trị của bạn đã được thiết lập thành công.
            </Typography>
            <Alert severity="success" sx={{ mb: 3 }}>
              Bạn có quyền truy cập đầy đủ vào hệ thống quản trị
            </Alert>
            {loading && (
              <Box display="flex" justifyContent="center" mb={2}>
                <CircularProgress size={24} />
              </Box>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  const renderActions = () => {
    return (
      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button
          onClick={handleBack}
          disabled={loading}
          startIcon={<ArrowBackIcon />}
          color="inherit"
        >
          Quay lại
        </Button>
        
        {currentStep === 0 ? (
          <Button
            onClick={handleNext}
            disabled={!adminType || loading}
            variant="contained"
            color="primary"
            size="large"
          >
            Tiếp tục
          </Button>
        ) : (
          <Button
            onClick={handleComplete}
            disabled={loading}
            variant="contained"
            color="primary"
            size="large"
            startIcon={loading ? <CircularProgress size={16} /> : <CheckCircleIcon />}
          >
            {loading ? 'Đang xử lý...' : 'Hoàn thành'}
          </Button>
        )}
      </DialogActions>
    );
  };

  return (
    <BaseRoleWelcomeDialog
      open={open}
      title="Thiết lập tài khoản Quản trị viên"
      icon={<AdminPanelSettingsIcon sx={{ fontSize: 48, color: theme.palette.error.main }} />}
      steps={steps}
      currentStep={currentStep}
      onBack={onBack}
      loading={loading}
      actions={renderActions()}
    >
      {renderStepContent()}
    </BaseRoleWelcomeDialog>
  );
};

export default AdminWelcomeDialog;
