"use client";

import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { setNoti } from '@/slices/notiSlice';
import { assignUserRole } from '@/actions/userAction';
import { fetchSubjects } from '@/actions/subjectAction';
import { http } from '@/lib/axios';
import { apiUrl } from '@/lib/fetcher';

import RoleSelectionDialog from './RoleSelectionDialog';
import StudentAgeSelectionDialog from './StudentAgeSelectionDialog';
import TeacherWelcomeDialog from './TeacherWelcomeDialog';
import TeacherPreferencesDialog from './TeacherPreferencesDialog';

const WelcomeDialogSystem = ({ user, onComplete }) => {
  const dispatch = useDispatch();
  const [currentStep, setCurrentStep] = useState('role-selection');
  const [selectedRole, setSelectedRole] = useState(null);
  const [loading, setLoading] = useState(false);
  const [subjects, setSubjects] = useState([]);
  const [grades, setGrades] = useState([]);

  // Load subjects and grades when component mounts
  useEffect(() => {
    const loadData = async () => {
      try {
        const [subjectsResponse, gradesResponse] = await Promise.all([
          fetchSubjects({ page: 1, limit: 100 }),
          http({
            url: apiUrl('/api/grades'),
            method: 'GET'
          })
        ]);

        if (subjectsResponse?.data) {
          setSubjects(subjectsResponse.data);
        }
        if (gradesResponse?.data) {
          setGrades(gradesResponse.data);
        }
      } catch (error) {
        console.error('Error loading subjects and grades:', error);
      }
    };

    loadData();
  }, []);

  const handleRoleSelection = (role) => {
    setSelectedRole(role);

    if (role === 'student') {
      setCurrentStep('student-age');
    } else if (role === 'teacher') {
      setCurrentStep('teacher-welcome');
    }
  };

  const handleStudentAgeSelection = async (age) => {
    setLoading(true);

    try {
      await assignUserRole({
        role: 'student',
        age: age
      });

      dispatch(setNoti({
        type: 'success',
        message: 'Chào mừng bạn đến với 2048.vn! Hãy bắt đầu học tập ngay.'
      }));

      onComplete();
    } catch (error) {
      dispatch(setNoti({
        type: 'error',
        message: error?.message || 'Đã xảy ra lỗi khi thiết lập tài khoản.'
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleTeacherWelcomeComplete = () => {
    setCurrentStep('teacher-preferences');
  };

  const handleTeacherPreferencesComplete = async (preferences) => {
    setLoading(true);

    try {
      await assignUserRole({
        role: 'teacher',
        subject_id: preferences.subjectId,
        grade_id: preferences.gradeId
      });

      dispatch(setNoti({
        type: 'success',
        message: 'Chào mừng bạn đến với 2048.vn! Hãy bắt đầu tạo nội dung giáo dục.'
      }));

      onComplete();
    } catch (error) {
      dispatch(setNoti({
        type: 'error',
        message: error?.message || 'Đã xảy ra lỗi khi thiết lập tài khoản.'
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (currentStep === 'student-age' || currentStep === 'teacher-welcome') {
      setCurrentStep('role-selection');
      setSelectedRole(null);
    } else if (currentStep === 'teacher-preferences') {
      setCurrentStep('teacher-welcome');
    }
  };

  return (
    <>
      <RoleSelectionDialog
        open={currentStep === 'role-selection'}
        onRoleSelect={handleRoleSelection}
        loading={loading}
      />

      <StudentAgeSelectionDialog
        open={currentStep === 'student-age'}
        onAgeSelect={handleStudentAgeSelection}
        onBack={handleBack}
        loading={loading}
      />

      <TeacherWelcomeDialog
        open={currentStep === 'teacher-welcome'}
        user={user}
        onComplete={handleTeacherWelcomeComplete}
        onBack={handleBack}
      />

      <TeacherPreferencesDialog
        open={currentStep === 'teacher-preferences'}
        subjects={subjects}
        grades={grades}
        onComplete={handleTeacherPreferencesComplete}
        onBack={handleBack}
        loading={loading}
      />
    </>
  );
};

export default WelcomeDialogSystem;
