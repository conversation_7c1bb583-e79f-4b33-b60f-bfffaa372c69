"use client";

import React from 'react';
import StudentWelcomeDialog from './dialogs/StudentWelcomeDialog';
import TeacherWelcomeDialog from './dialogs/TeacherWelcomeDialog';
import AdminWelcomeDialog from './dialogs/AdminWelcomeDialog';
import TrainerWelcomeDialog from './dialogs/TrainerWelcomeDialog';

/**
 * Factory component for creating role-specific welcome dialogs
 * This pattern makes it easy to add new role types without modifying existing code
 */
const RoleDialogFactory = ({ 
  role, 
  open, 
  user, 
  subjects = [], 
  grades = [], 
  onComplete, 
  onBack, 
  loading = false 
}) => {
  
  const roleDialogs = {
    student: StudentWelcomeDialog,
    teacher: TeacherWelcomeDialog,
    admin: AdminWelcomeDialog,
    trainer: TrainerWelcomeDialog,
    // Easy to add new roles here:
    // editor: EditorWelcomeDialog,
    // moderator: ModeratorWelcomeDialog,
    // etc.
  };

  const DialogComponent = roleDialogs[role];

  if (!DialogComponent) {
    console.warn(`No welcome dialog found for role: ${role}`);
    return null;
  }

  // Common props that all role dialogs should receive
  const commonProps = {
    open,
    onComplete,
    onBack,
    loading
  };

  // Role-specific props
  const roleSpecificProps = {
    student: {},
    teacher: { user, subjects, grades },
    admin: {},
    trainer: {},
  };

  return (
    <DialogComponent
      {...commonProps}
      {...(roleSpecificProps[role] || {})}
    />
  );
};

export default RoleDialogFactory;
