"use client";

import React from 'react';

import { useTheme } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardActionArea from '@mui/material/CardActionArea';
import Grid from '@mui/material/Grid';
import CircularProgress from '@mui/material/CircularProgress';

import SchoolIcon from '@mui/icons-material/School';
import PersonIcon from '@mui/icons-material/Person';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import EditIcon from '@mui/icons-material/Edit';

const RoleSelectionDialog = ({ open, onRoleSelect, loading, availableRoles = ['student', 'teacher'] }) => {
  const theme = useTheme();

  const allRoleOptions = {
    student: {
      id: 'student',
      title: 'Học sinh',
      description: 'Tham gia các bài kiểm tra và học tập',
      icon: <SchoolIcon sx={{ fontSize: 48, color: theme.palette.primary.main }} />,
      color: theme.palette.primary.main
    },
    teacher: {
      id: 'teacher',
      title: 'Giáo viên',
      description: 'Tạo và quản lý nội dung giáo dục',
      icon: <PersonIcon sx={{ fontSize: 48, color: theme.palette.secondary.main }} />,
      color: theme.palette.secondary.main
    },
    admin: {
      id: 'admin',
      title: 'Quản trị viên',
      description: 'Quản lý hệ thống và người dùng',
      icon: <AdminPanelSettingsIcon sx={{ fontSize: 48, color: theme.palette.error.main }} />,
      color: theme.palette.error.main
    },
    trainer: {
      id: 'trainer',
      title: 'Trainer',
      description: 'Đào tạo và phát triển kỹ năng',
      icon: <FitnessCenterIcon sx={{ fontSize: 48, color: theme.palette.warning.main }} />,
      color: theme.palette.warning.main
    },
    editor: {
      id: 'editor',
      title: 'Biên tập viên',
      description: 'Tạo và chỉnh sửa nội dung',
      icon: <EditIcon sx={{ fontSize: 48, color: theme.palette.info.main }} />,
      color: theme.palette.info.main
    }
  };

  // Filter available roles based on props
  const roleOptions = availableRoles.map(roleId => allRoleOptions[roleId]).filter(Boolean);

  const handleRoleSelect = (roleId) => {
    if (!loading) {
      onRoleSelect(roleId);
    }
  };

  return (
    <Dialog
      open={open}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 2
        }
      }}
    >
      <DialogContent sx={{ p: 4 }}>
        <Box textAlign="center" mb={4}>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Chào mừng đến với 2048.vn
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Hãy chọn vai trò của bạn để bắt đầu
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {roleOptions.map((role) => (
            <Grid item xs={12} sm={6} key={role.id}>
              <Card
                sx={{
                  height: '100%',
                  border: `2px solid transparent`,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    border: `2px solid ${role.color}`,
                    transform: 'translateY(-4px)',
                    boxShadow: theme.shadows[8]
                  }
                }}
              >
                <CardActionArea
                  onClick={() => handleRoleSelect(role.id)}
                  disabled={loading}
                  sx={{
                    height: '100%',
                    p: 3,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: 200
                  }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 0 }}>
                    <Box mb={2}>
                      {role.icon}
                    </Box>
                    <Typography variant="h5" component="h2" gutterBottom fontWeight="bold">
                      {role.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {role.description}
                    </Typography>
                  </CardContent>
                </CardActionArea>
              </Card>
            </Grid>
          ))}
        </Grid>

        {loading && (
          <Box display="flex" justifyContent="center" mt={3}>
            <CircularProgress size={24} />
          </Box>
        )}

        <Box mt={4} textAlign="center">
          <Typography variant="caption" color="text.secondary">
            Bạn có thể thay đổi vai trò này sau trong cài đặt tài khoản
          </Typography>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default RoleSelectionDialog;
