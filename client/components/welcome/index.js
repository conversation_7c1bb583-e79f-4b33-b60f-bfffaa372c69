// Main system
export { default as WelcomeDialogSystem } from './WelcomeDialogSystem';
export { default as RoleSelectionDialog } from './RoleSelectionDialog';
export { default as RoleDialogFactory } from './RoleDialogFactory';

// Role-specific dialogs
export { default as StudentWelcomeDialog } from './dialogs/StudentWelcomeDialog';
export { default as TeacherWelcomeDialog } from './dialogs/TeacherWelcomeDialog';
export { default as AdminWelcomeDialog } from './dialogs/AdminWelcomeDialog';
export { default as TrainerWelcomeDialog } from './dialogs/TrainerWelcomeDialog';

// Base components
export { default as BaseRoleWelcomeDialog } from './dialogs/BaseRoleWelcomeDialog';

// Legacy components (for backward compatibility)
export { default as StudentAgeSelectionDialog } from './StudentAgeSelectionDialog';
export { default as TeacherPreferencesDialog } from './TeacherPreferencesDialog';
