"use client";

import React, { useState } from 'react';

import { useTheme } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Grid from '@mui/material/Grid';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PersonIcon from '@mui/icons-material/Person';

const TeacherPreferencesDialog = ({ open, subjects, grades, onComplete, onBack, loading }) => {
  const theme = useTheme();
  const [selectedSubject, setSelectedSubject] = useState('');
  const [selectedGrade, setSelectedGrade] = useState('');
  const [errors, setErrors] = useState({});

  const handleSubjectChange = (event) => {
    setSelectedSubject(event.target.value);
    if (errors.subject) {
      setErrors(prev => ({ ...prev, subject: null }));
    }
  };

  const handleGradeChange = (event) => {
    setSelectedGrade(event.target.value);
    if (errors.grade) {
      setErrors(prev => ({ ...prev, grade: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!selectedSubject) {
      newErrors.subject = 'Vui lòng chọn môn học';
    }

    if (!selectedGrade) {
      newErrors.grade = 'Vui lòng chọn lớp';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleComplete = () => {
    if (validateForm() && !loading) {
      onComplete({
        subjectId: selectedSubject,
        gradeId: selectedGrade
      });
    }
  };

  const handleBack = () => {
    if (!loading) {
      onBack();
    }
  };

  return (
    <Dialog
      open={open}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 2
        }
      }}
    >
      <DialogContent sx={{ p: 4 }}>
        <Box textAlign="center" mb={4}>
          <Box mb={2}>
            <PersonIcon sx={{ fontSize: 48, color: theme.palette.secondary.main }} />
          </Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Thiết lập thông tin giảng dạy
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Hãy cho chúng tôi biết môn học và lớp bạn thường giảng dạy
          </Typography>
        </Box>

        <Grid container spacing={3} mb={3}>
          <Grid item xs={12}>
            <FormControl fullWidth error={!!errors.subject}>
              <InputLabel id="subject-select-label">Môn học *</InputLabel>
              <Select
                labelId="subject-select-label"
                value={selectedSubject}
                label="Môn học *"
                onChange={handleSubjectChange}
                disabled={loading}
              >
                {subjects.map((subject) => (
                  <MenuItem key={subject.id} value={subject.id}>
                    {subject.title}
                  </MenuItem>
                ))}
              </Select>
              {errors.subject && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1 }}>
                  {errors.subject}
                </Typography>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth error={!!errors.grade}>
              <InputLabel id="grade-select-label">Lớp *</InputLabel>
              <Select
                labelId="grade-select-label"
                value={selectedGrade}
                label="Lớp *"
                onChange={handleGradeChange}
                disabled={loading}
              >
                {grades.map((grade) => (
                  <MenuItem key={grade.id} value={grade.id}>
                    {grade.title}
                  </MenuItem>
                ))}
              </Select>
              {errors.grade && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1 }}>
                  {errors.grade}
                </Typography>
              )}
            </FormControl>
          </Grid>
        </Grid>

        <Alert severity="info" sx={{ mb: 3 }}>
          Bạn có thể thay đổi thông tin này sau trong cài đặt tài khoản
        </Alert>

        {loading && (
          <Box display="flex" justifyContent="center" mb={2}>
            <CircularProgress size={24} />
          </Box>
        )}

        <Box textAlign="center" mt={2}>
          <Typography variant="body2" color="text.secondary">
            Bước 2 của 2
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button
          onClick={handleBack}
          disabled={loading}
          startIcon={<ArrowBackIcon />}
          color="inherit"
        >
          Quay lại
        </Button>
        <Button
          onClick={handleComplete}
          disabled={loading || !selectedSubject || !selectedGrade}
          variant="contained"
          color="primary"
          size="large"
          startIcon={loading ? <CircularProgress size={16} /> : <CheckCircleIcon />}
        >
          {loading ? 'Đang xử lý...' : 'Hoàn thành'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TeacherPreferencesDialog;
