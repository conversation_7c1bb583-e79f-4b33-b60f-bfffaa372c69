"use client";

import React, { useState, useEffect } from 'react';

import { useTheme } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Avatar from '@mui/material/Avatar';
import LinearProgress from '@mui/material/LinearProgress';

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

const TeacherWelcomeDialog = ({ open, user, onComplete, onBack }) => {
  const theme = useTheme();
  const [progress, setProgress] = useState(0);
  const [autoAdvancing, setAutoAdvancing] = useState(false);

  useEffect(() => {
    if (open) {
      setProgress(0);
      setAutoAdvancing(true);

      const timer = setInterval(() => {
        setProgress((prevProgress) => {
          const newProgress = prevProgress + (100 / 30); // 3 seconds = 30 intervals of 100ms

          if (newProgress >= 100) {
            clearInterval(timer);
            setAutoAdvancing(false);
            setTimeout(() => {
              onComplete();
            }, 200);
            return 100;
          }

          return newProgress;
        });
      }, 100);

      return () => {
        clearInterval(timer);
        setAutoAdvancing(false);
      };
    }
  }, [open, onComplete]);

  const handleManualNext = () => {
    if (!autoAdvancing) {
      onComplete();
    }
  };

  const handleBack = () => {
    if (!autoAdvancing) {
      onBack();
    }
  };

  return (
    <Dialog
      open={open}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 2
        }
      }}
    >
      <DialogContent sx={{ p: 4, textAlign: 'center' }}>
        <Box mb={3}>
          <Avatar
            src={user?.avatar}
            alt={user?.name}
            sx={{
              width: 60,
              height: 60,
              mx: 'auto',
              mb: 2,
              border: `3px solid ${theme.palette.primary.main}`
            }}
          >
            {user?.name?.charAt(0)?.toUpperCase()}
          </Avatar>
        </Box>

        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Chào mừng đến với 2048.vn
        </Typography>

        <Typography variant="h6" color="text.secondary" mb={4}>
          TuanDung IT Hãy cùng cá nhân hóa trải nghiệm trên 2048.vn của bạn
        </Typography>

        <Box mb={3}>
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: theme.palette.grey[200],
              '& .MuiLinearProgress-bar': {
                borderRadius: 3,
                backgroundColor: theme.palette.primary.main
              }
            }}
          />
        </Box>

        <Typography variant="body2" color="text.secondary">
          Bước 1 của 2
        </Typography>

        {autoAdvancing && (
          <Typography variant="caption" color="text.secondary" mt={1} display="block">
            Tự động chuyển sau {Math.ceil((100 - progress) / (100 / 3))} giây...
          </Typography>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button
          onClick={handleBack}
          disabled={autoAdvancing}
          startIcon={<ArrowBackIcon />}
          color="inherit"
        >
          Quay lại
        </Button>
        <Button
          onClick={handleManualNext}
          disabled={autoAdvancing}
          variant="contained"
          color="primary"
          endIcon={<ArrowForwardIcon />}
        >
          Tiếp tục
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TeacherWelcomeDialog;
