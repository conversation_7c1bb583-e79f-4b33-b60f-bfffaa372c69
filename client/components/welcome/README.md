# Welcome Dialog System

A modular and extensible welcome dialog system for first-time user onboarding with role-based flows.

## Architecture Overview

The system is designed with modularity and extensibility in mind, making it easy to add new role types without modifying existing code.

### Core Components

1. **WelcomeDialogSystem** - Main orchestrator component
2. **RoleSelectionDialog** - Initial role selection interface
3. **RoleDialogFactory** - Factory pattern for role-specific dialogs
4. **BaseRoleWelcomeDialog** - Base component for creating new role dialogs

### Role-Specific Dialogs

- **StudentWelcomeDialog** - Complete student onboarding flow
- **TeacherWelcomeDialog** - Teacher onboarding with multi-selection preferences
- **AdminWelcomeDialog** - Admin setup with admin type selection
- **TrainerWelcomeDialog** - Trainer onboarding with specialization

## Features

### Multi-Selection Support
- Teachers can select multiple subjects and grades
- Data stored as JSON arrays in database
- Backward compatibility with single-selection data

### Extensible Architecture
- Easy to add new role types
- Factory pattern for role dialog management
- Consistent UI/UX patterns across all roles

### Database Schema
```sql
-- New fields for multi-selection
preferred_subjects JSON NULL,
preferred_grades JSON NULL,

-- Existing fields maintained for backward compatibility
preferred_subject_id BIGINT NULL,
preferred_grade_id BIGINT NULL
```

## Usage

### Basic Implementation

```jsx
import { WelcomeDialogSystem } from '@/components/welcome';

function Dashboard() {
  return (
    <DashboardClient availableRoles={['student', 'teacher']}>
      {/* Dashboard content */}
    </DashboardClient>
  );
}
```

### Advanced Configuration

```jsx
// Enable all role types
<DashboardClient availableRoles={['student', 'teacher', 'admin', 'trainer']}>
  {/* Content */}
</DashboardClient>

// Custom role selection
<WelcomeDialogSystem
  user={user}
  availableRoles={['teacher', 'admin']}
  onComplete={handleComplete}
/>
```

## Adding New Role Types

### 1. Create Role Dialog Component

```jsx
// components/welcome/dialogs/NewRoleWelcomeDialog.js
import BaseRoleWelcomeDialog from './BaseRoleWelcomeDialog';

const NewRoleWelcomeDialog = ({ open, onComplete, onBack, loading }) => {
  // Implementation
  return (
    <BaseRoleWelcomeDialog
      open={open}
      title="New Role Setup"
      steps={['Step 1', 'Step 2']}
      // ... other props
    >
      {/* Role-specific content */}
    </BaseRoleWelcomeDialog>
  );
};
```

### 2. Register in Factory

```jsx
// RoleDialogFactory.js
const roleDialogs = {
  student: StudentWelcomeDialog,
  teacher: TeacherWelcomeDialog,
  newrole: NewRoleWelcomeDialog, // Add here
};
```

### 3. Add to Role Selection

```jsx
// RoleSelectionDialog.js
const allRoleOptions = {
  // ... existing roles
  newrole: {
    id: 'newrole',
    title: 'New Role',
    description: 'Description of new role',
    icon: <NewRoleIcon />,
    color: theme.palette.success.main
  }
};
```

### 4. Update Backend Validation

```php
// UserController.php
$request->validate([
    'role' => 'required|string|in:student,teacher,admin,editor,newrole',
    // ... other validation rules
]);
```

## API Endpoints

### Assign Role (Multi-Selection)

```javascript
POST /api/user/assign-role

// Teacher with multiple subjects/grades
{
  "role": "teacher",
  "subject_ids": [1, 2, 3],
  "grade_ids": [4, 5]
}

// Student with age
{
  "role": "student",
  "age": 15
}

// Backward compatibility (single selection)
{
  "role": "teacher",
  "subject_id": 1,
  "grade_id": 4
}
```

## Database Migration

Run the migration to add multi-selection support:

```bash
php artisan migrate
```

This adds:
- `preferred_subjects` JSON field
- `preferred_grades` JSON field
- Migrates existing single-selection data to multi-selection format

## Component Props

### WelcomeDialogSystem

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| user | Object | - | Current user object |
| availableRoles | Array | ['student', 'teacher'] | Available role options |
| onComplete | Function | - | Callback when onboarding completes |

### RoleDialogFactory

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| role | String | - | Selected role type |
| open | Boolean | false | Dialog open state |
| user | Object | - | Current user object |
| subjects | Array | [] | Available subjects |
| grades | Array | [] | Available grades |
| onComplete | Function | - | Completion callback |
| onBack | Function | - | Back navigation callback |
| loading | Boolean | false | Loading state |

## Best Practices

1. **Consistent UI/UX** - Use BaseRoleWelcomeDialog for consistent styling
2. **Validation** - Always validate form inputs before submission
3. **Loading States** - Show loading indicators during API calls
4. **Error Handling** - Provide user-friendly error messages
5. **Backward Compatibility** - Maintain support for existing data structures
6. **Extensibility** - Design components to be easily extended

## Testing

Test each role flow:

1. **Student Path**: Age selection → Role assignment
2. **Teacher Path**: Welcome screen → Multi-selection preferences → Role assignment
3. **Admin Path**: Admin type selection → Role assignment
4. **Trainer Path**: Specialization setup → Role assignment

Verify:
- Form validation works correctly
- Multi-selection data is saved properly
- Backward compatibility is maintained
- Error handling functions as expected
- Loading states display correctly
