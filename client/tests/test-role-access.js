// Test script to verify role-based access control
// This is a simple test to verify the helper functions work correctly

// Mock user objects for testing
const adminUser = {
  id: 1,
  name: 'Admin User',
  role: [{ name: 'admin' }]
};

const editorUser = {
  id: 2,
  name: 'Editor User', 
  role: [{ name: 'editor' }]
};

const teacherUser = {
  id: 3,
  name: 'Teacher User',
  role: [{ name: 'teacher' }]
};

const studentUser = {
  id: 4,
  name: 'Student User',
  role: [{ name: 'student' }]
};

// Helper functions (copied from utils/helpers.js for testing)
const hasRole = (user, roles) => {
  if (!user || !user.role) return false;
  
  const userRoles = Array.isArray(user.role) ? user.role : [user.role];
  const rolesToCheck = Array.isArray(roles) ? roles : [roles];
  
  return rolesToCheck.some(role => 
    userRoles.some(userRole => 
      (typeof userRole === 'string' ? userRole : userRole.name) === role
    )
  );
};

const isStudent = (user) => {
  return hasRole(user, 'student');
};

const canAccessEducationalFeatures = (user) => {
  return !isStudent(user);
};

// Test cases
console.log('=== Role-based Access Control Tests ===\n');

// Test admin user
console.log('Admin User Tests:');
console.log('- hasRole(admin, [admin, editor, teacher]):', hasRole(adminUser, ['admin', 'editor', 'teacher']));
console.log('- isStudent(admin):', isStudent(adminUser));
console.log('- canAccessEducationalFeatures(admin):', canAccessEducationalFeatures(adminUser));
console.log('');

// Test editor user
console.log('Editor User Tests:');
console.log('- hasRole(editor, [admin, editor, teacher]):', hasRole(editorUser, ['admin', 'editor', 'teacher']));
console.log('- isStudent(editor):', isStudent(editorUser));
console.log('- canAccessEducationalFeatures(editor):', canAccessEducationalFeatures(editorUser));
console.log('');

// Test teacher user
console.log('Teacher User Tests:');
console.log('- hasRole(teacher, [admin, editor, teacher]):', hasRole(teacherUser, ['admin', 'editor', 'teacher']));
console.log('- isStudent(teacher):', isStudent(teacherUser));
console.log('- canAccessEducationalFeatures(teacher):', canAccessEducationalFeatures(teacherUser));
console.log('');

// Test student user
console.log('Student User Tests:');
console.log('- hasRole(student, [admin, editor, teacher]):', hasRole(studentUser, ['admin', 'editor', 'teacher']));
console.log('- isStudent(student):', isStudent(studentUser));
console.log('- canAccessEducationalFeatures(student):', canAccessEducationalFeatures(studentUser));
console.log('');

// Test menu filtering logic
const mockMenuItems = [
  {
    id: 'my-collections',
    title: 'Học liệu của tôi',
    roles: ['admin', 'editor', 'teacher']
  },
  {
    id: 'classes', 
    title: 'Quản lý lớp học',
    roles: ['admin', 'editor', 'teacher']
  },
  {
    id: 'report',
    title: 'Báo cáo',
    roles: ['admin', 'editor', 'teacher']
  },
  {
    id: 'dashboard',
    title: 'Khám phá'
    // No roles restriction
  }
];

const filterMenuItems = (items, user) => {
  return items.filter(item => {
    if (item.roles && item.roles.length > 0) {
      return hasRole(user, item.roles);
    }
    return true;
  });
};

console.log('=== Menu Filtering Tests ===\n');

console.log('Admin can see menus:', filterMenuItems(mockMenuItems, adminUser).map(item => item.title));
console.log('Editor can see menus:', filterMenuItems(mockMenuItems, editorUser).map(item => item.title));
console.log('Teacher can see menus:', filterMenuItems(mockMenuItems, teacherUser).map(item => item.title));
console.log('Student can see menus:', filterMenuItems(mockMenuItems, studentUser).map(item => item.title));

console.log('\n=== Expected Results ===');
console.log('Admin, Editor, Teacher should see all 4 menus');
console.log('Student should see only 1 menu (Khám phá)');
