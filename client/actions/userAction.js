import { apiUrl, fetcher } from "@/lib/fetcher";
import { http } from "@/lib/axios";

/*
  ================ Server Action ================
  - fetcher với option cache: "force-cache" chỉ có tác dụng với server component
  - c<PERSON> thể call server action ở client component, lúc đó cache: "force-cache" sẽ không có tác dụng
*/

/* ================ Client Action ================ */
export const fetchRecentActivity = async (params = {}, signal) => {
  const headers = params?.token ? { Authorization: `Bearer ${params.token}` } : {};

  return await http({
    url: apiUrl("/api/user/recent-activity"),
    method: "GET",
    params,
    headers,
    signal,
  });
};

export const fetchGoogleUrl = async (redirect = "/") => {
  try {
    const response = await http({
      url: apiUrl(`/social/google?redirect=${encodeURIComponent(redirect)}`),
      method: "GET",
      withCredentials: true,
    });

    if (!response || typeof response.url !== "string") {
      throw new Error("Phản hồi backend không đúng định dạng");
    }

    return { url: response.url };
  } catch (error) {
    console.error("Lỗi khi lấy Google login URL:", error);
    throw error;
  }
};

export const updateProfile = async (data) => {
  return await http({
    url: apiUrl("/api/user/update-profile"),
    method: "PUT",
    data
  })
};

export const updatePassword = async (data) => {
  return await http({
    url: apiUrl("/api/user/change-password"),
    method: "POST",
    data
  })
};

export const fetchAssignmentsReport = async (params = {}) => {
  return await http({
    url: apiUrl(`/api/private/assignments/report`),
    method: "GET",
    params,
  });
};

export const assignUserRole = async (data) => {
  return await http({
    url: apiUrl("/api/user/assign-role"),
    method: "POST",
    data
  });
};
